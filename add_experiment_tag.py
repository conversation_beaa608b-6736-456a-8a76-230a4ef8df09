#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add a tag to an existing Braintrust experiment.

This script demonstrates how to:
1. Open an existing experiment using braintrust.init() with open=True
2. Use the Braintrust API to update the experiment's tags
3. Handle both experiment name and experiment ID lookups

Usage:
    python add_experiment_tag.py --project "My Project" --experiment "My Experiment" --tag "new-tag"
    python add_experiment_tag.py --project "My Project" --experiment-id "exp-123" --tag "new-tag"
"""

import argparse
import sys
from typing import List, Optional

import braintrust


def add_tag_to_experiment(
    project: str,
    experiment: Optional[str] = None,
    experiment_id: Optional[str] = None,
    tag: str = "",
    api_key: Optional[str] = None,
    app_url: Optional[str] = None,
) -> bool:
    """
    Add a tag to an existing Braintrust experiment.
    
    Args:
        project: Name of the project containing the experiment
        experiment: Name of the experiment (if using name-based lookup)
        experiment_id: ID of the experiment (if using ID-based lookup)
        tag: Tag to add to the experiment
        api_key: Braintrust API key (optional, can use environment variable)
        app_url: Braintrust app URL (optional, defaults to production)
    
    Returns:
        True if successful, False otherwise
    """
    
    if not experiment and not experiment_id:
        print("Error: Must provide either experiment name or experiment ID")
        return False
    
    if not tag:
        print("Error: Must provide a tag to add")
        return False
    
    try:
        # First, open the existing experiment to get its current state
        if experiment:
            print(f"Opening experiment '{experiment}' in project '{project}'...")
            exp = braintrust.init(
                project=project,
                experiment=experiment,
                open=True,  # This opens an existing experiment in read-only mode
                api_key=api_key,
                app_url=app_url,
            )
        else:
            # For experiment ID, we need to get the experiment info first
            print(f"Looking up experiment with ID '{experiment_id}'...")
            braintrust.login(api_key=api_key, app_url=app_url)
            
            # Get experiment info using the API
            state = braintrust._internal_get_global_state()
            response = state.app_conn().post_json("api/experiment/get", {
                "experiment_id": experiment_id,
                "org_name": state.org_name,
            })
            
            if not response:
                print(f"Error: Experiment with ID '{experiment_id}' not found")
                return False
            
            exp_info = response[0]
            exp = braintrust.init(
                project=exp_info.get("project_name", project),
                experiment=exp_info["name"],
                open=True,
                api_key=api_key,
                app_url=app_url,
            )
        
        # Get the experiment ID and current tags
        exp_id = exp.id
        exp_name = exp.name
        
        print(f"Found experiment: {exp_name} (ID: {exp_id})")
        
        # Get current experiment metadata by fetching the full experiment object
        state = braintrust._internal_get_global_state()
        current_exp_response = state.app_conn().post_json("api/experiment/get", {
            "experiment_id": exp_id,
            "org_name": state.org_name,
        })
        
        if not current_exp_response:
            print(f"Error: Could not fetch current experiment data")
            return False
        
        current_exp = current_exp_response[0]
        current_tags = current_exp.get("tags", []) or []
        
        print(f"Current tags: {current_tags}")
        
        # Check if tag already exists
        if tag in current_tags:
            print(f"Tag '{tag}' already exists on experiment '{exp_name}'")
            return True
        
        # Add the new tag
        new_tags = current_tags + [tag]
        
        print(f"Adding tag '{tag}' to experiment '{exp_name}'...")
        
        # Update the experiment using the PATCH API
        patch_data = {
            "tags": new_tags
        }
        
        # Use the app connection to make a PATCH request
        response = state.app_conn().session.patch(
            f"{state.app_url}/api/experiment/{exp_id}",
            json=patch_data,
            headers={"Authorization": f"Bearer {state.login_token}"}
        )
        
        if response.status_code == 200:
            print(f"Successfully added tag '{tag}' to experiment '{exp_name}'")
            print(f"New tags: {new_tags}")
            return True
        else:
            print(f"Error updating experiment: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Add a tag to an existing Braintrust experiment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Add tag using experiment name
  python add_experiment_tag.py --project "My Project" --experiment "My Experiment" --tag "production"
  
  # Add tag using experiment ID
  python add_experiment_tag.py --project "My Project" --experiment-id "exp-123" --tag "production"
  
  # Specify custom API key and app URL
  python add_experiment_tag.py --project "My Project" --experiment "My Experiment" --tag "production" --api-key "your-key" --app-url "https://custom.braintrust.dev"
        """
    )
    
    parser.add_argument(
        "--project",
        required=True,
        help="Name of the project containing the experiment"
    )
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        "--experiment",
        help="Name of the experiment to update"
    )
    group.add_argument(
        "--experiment-id",
        help="ID of the experiment to update"
    )
    
    parser.add_argument(
        "--tag",
        required=True,
        help="Tag to add to the experiment"
    )
    
    parser.add_argument(
        "--api-key",
        help="Braintrust API key (can also use BRAINTRUST_API_KEY environment variable)"
    )
    
    parser.add_argument(
        "--app-url",
        help="Braintrust app URL (defaults to https://www.braintrust.dev)"
    )
    
    args = parser.parse_args()
    
    success = add_tag_to_experiment(
        project=args.project,
        experiment=args.experiment,
        experiment_id=args.experiment_id,
        tag=args.tag,
        api_key=args.api_key,
        app_url=args.app_url,
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
