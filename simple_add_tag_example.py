#!/usr/bin/env python3
"""
Simple example showing how to add a tag to an existing Braintrust experiment.

This example demonstrates the core functionality in a concise way.
"""

import braintrust


def add_tag_to_experiment_simple(project_name: str, experiment_name: str, new_tag: str):
    """
    Add a tag to an existing experiment using the Braintrust Python SDK.
    
    Args:
        project_name: Name of the project containing the experiment
        experiment_name: Name of the experiment to update
        new_tag: Tag to add to the experiment
    """
    
    # Step 1: Open the existing experiment in read-only mode
    print(f"Opening experiment '{experiment_name}' in project '{project_name}'...")
    
    experiment = braintrust.init(
        project=project_name,
        experiment=experiment_name,
        open=True  # This opens an existing experiment
    )
    
    print(f"Found experiment: {experiment.name} (ID: {experiment.id})")
    
    # Step 2: Get the current experiment data including tags
    state = braintrust._internal_get_global_state()
    
    # Fetch current experiment metadata
    response = state.app_conn().post_json("api/experiment/get", {
        "experiment_id": experiment.id,
        "org_name": state.org_name,
    })
    
    if not response:
        raise Exception("Could not fetch experiment data")
    
    current_experiment = response[0]
    current_tags = current_experiment.get("tags", []) or []
    
    print(f"Current tags: {current_tags}")
    
    # Step 3: Check if tag already exists
    if new_tag in current_tags:
        print(f"Tag '{new_tag}' already exists!")
        return
    
    # Step 4: Add the new tag
    updated_tags = current_tags + [new_tag]
    
    print(f"Adding tag '{new_tag}'...")
    
    # Step 5: Update the experiment using the REST API
    patch_response = state.app_conn().session.patch(
        f"{state.app_url}/api/experiment/{experiment.id}",
        json={"tags": updated_tags},
        headers={"Authorization": f"Bearer {state.login_token}"}
    )
    
    if patch_response.status_code == 200:
        print(f"✅ Successfully added tag '{new_tag}'!")
        print(f"Updated tags: {updated_tags}")
    else:
        print(f"❌ Error: {patch_response.status_code} - {patch_response.text}")
        raise Exception(f"Failed to update experiment: {patch_response.text}")


if __name__ == "__main__":
    # Example usage
    PROJECT_NAME = "My Project"  # Replace with your project name
    EXPERIMENT_NAME = "My Experiment"  # Replace with your experiment name
    NEW_TAG = "production"  # Replace with the tag you want to add
    
    try:
        add_tag_to_experiment_simple(PROJECT_NAME, EXPERIMENT_NAME, NEW_TAG)
    except Exception as e:
        print(f"Error: {e}")
