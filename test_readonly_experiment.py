#!/usr/bin/env python3
"""
Test script to verify ReadonlyExperiment behavior.
This helps debug the attribute access issue.
"""

import braintrust

def test_readonly_experiment():
    """Test what properties are available on ReadonlyExperiment"""
    
    # You'll need to replace these with actual values from your Braintrust account
    PROJECT_NAME = "My Project"  # Replace with your project name
    EXPERIMENT_NAME = "My Experiment"  # Replace with your experiment name
    
    try:
        print("Testing ReadonlyExperiment properties...")
        
        # Open an existing experiment
        experiment = braintrust.init(
            project=PROJECT_NAME,
            experiment=EXPERIMENT_NAME,
            open=True
        )
        
        print(f"Experiment object type: {type(experiment)}")
        print(f"Available attributes: {dir(experiment)}")
        
        # Test accessing id
        try:
            exp_id = experiment.id
            print(f"✅ experiment.id works: {exp_id}")
        except Exception as e:
            print(f"❌ experiment.id failed: {e}")
        
        # Test accessing name (this should fail)
        try:
            exp_name = experiment.name
            print(f"✅ experiment.name works: {exp_name}")
        except Exception as e:
            print(f"❌ experiment.name failed: {e}")
        
        # Test accessing _lazy_metadata
        try:
            metadata = experiment._lazy_metadata.get()
            exp_name_from_metadata = metadata.experiment.name
            print(f"✅ experiment._lazy_metadata.get().experiment.name works: {exp_name_from_metadata}")
        except Exception as e:
            print(f"❌ accessing name via _lazy_metadata failed: {e}")
            
    except Exception as e:
        print(f"Error during test: {e}")
        print("Make sure to update PROJECT_NAME and EXPERIMENT_NAME with real values")

if __name__ == "__main__":
    test_readonly_experiment()
