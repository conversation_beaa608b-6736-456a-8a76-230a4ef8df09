#!/usr/bin/env python3
"""
Example showing how to add a tag to an experiment using the experiment ID.

This addresses the question about whether braintrust.init can work with experiment IDs.
The answer is: braintrust.init() requires experiment names, but you can look up 
experiment details by ID using the API and then use the name.
"""

import braintrust


def add_tag_by_experiment_id(experiment_id: str, new_tag: str, project_name: str = None):
    """
    Add a tag to an experiment using its ID instead of name.
    
    Args:
        experiment_id: The UUID of the experiment
        new_tag: Tag to add to the experiment  
        project_name: Optional project name (will be looked up if not provided)
    """
    
    # Step 1: Login to Braintrust to get access to the API
    braintrust.login()
    state = braintrust._internal_get_global_state()
    
    print(f"Looking up experiment with ID: {experiment_id}")
    
    # Step 2: Get experiment details by ID
    # Note: braintrust.init() doesn't directly support experiment IDs,
    # so we need to look up the experiment name first
    response = state.app_conn().post_json("api/experiment/get", {
        "experiment_id": experiment_id,
        "org_name": state.org_name,
    })
    
    if not response:
        raise Exception(f"Experiment with ID '{experiment_id}' not found")
    
    experiment_data = response[0]
    experiment_name = experiment_data["name"]
    project_id = experiment_data["project_id"]
    
    # Get project name if not provided
    if not project_name:
        project_response = state.app_conn().post_json("api/project/get", {
            "project_id": project_id,
            "org_name": state.org_name,
        })
        if project_response:
            project_name = project_response[0]["name"]
        else:
            raise Exception(f"Could not find project for experiment")
    
    print(f"Found experiment: '{experiment_name}' in project '{project_name}'")
    
    # Step 3: Now we can use braintrust.init() with the experiment name
    experiment = braintrust.init(
        project=project_name,
        experiment=experiment_name,
        open=True
    )
    
    # Step 4: Get current tags
    current_tags = experiment_data.get("tags", []) or []
    print(f"Current tags: {current_tags}")
    
    # Step 5: Check if tag already exists
    if new_tag in current_tags:
        print(f"Tag '{new_tag}' already exists!")
        return
    
    # Step 6: Add the new tag
    updated_tags = current_tags + [new_tag]
    print(f"Adding tag '{new_tag}'...")
    
    # Step 7: Update the experiment
    patch_response = state.app_conn().session.patch(
        f"{state.app_url}/api/experiment/{experiment_id}",
        json={"tags": updated_tags},
        headers={"Authorization": f"Bearer {state.login_token}"}
    )
    
    if patch_response.status_code == 200:
        print(f"✅ Successfully added tag '{new_tag}' to experiment!")
        print(f"Updated tags: {updated_tags}")
    else:
        print(f"❌ Error: {patch_response.status_code} - {patch_response.text}")
        raise Exception(f"Failed to update experiment: {patch_response.text}")


def list_experiments_in_project(project_name: str):
    """
    Helper function to list all experiments in a project with their IDs.
    Useful for finding the experiment ID you want to update.
    """
    braintrust.login()
    state = braintrust._internal_get_global_state()
    
    # Get project ID first
    project_response = state.app_conn().post_json("api/project/get", {
        "project_name": project_name,
        "org_name": state.org_name,
    })
    
    if not project_response:
        print(f"Project '{project_name}' not found")
        return
    
    project_id = project_response[0]["id"]
    
    # List experiments in the project
    experiments_response = state.app_conn().post_json("api/experiment/list", {
        "project_id": project_id,
        "org_name": state.org_name,
    })
    
    print(f"\nExperiments in project '{project_name}':")
    print("-" * 60)
    for exp in experiments_response.get("objects", []):
        tags = exp.get("tags", [])
        tags_str = f" (tags: {tags})" if tags else ""
        print(f"Name: {exp['name']}")
        print(f"ID: {exp['id']}{tags_str}")
        print("-" * 60)


if __name__ == "__main__":
    # Example usage
    
    # First, list experiments to find the ID you want
    PROJECT_NAME = "My Project"  # Replace with your project name
    print("Listing experiments to find IDs...")
    list_experiments_in_project(PROJECT_NAME)
    
    # Then add a tag using the experiment ID
    EXPERIMENT_ID = "your-experiment-id-here"  # Replace with actual experiment ID
    NEW_TAG = "updated-via-id"
    
    # Uncomment the line below and replace with a real experiment ID to test
    # add_tag_by_experiment_id(EXPERIMENT_ID, NEW_TAG, PROJECT_NAME)
